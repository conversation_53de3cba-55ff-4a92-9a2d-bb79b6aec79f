<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>{$zt_name}</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <img src="img/logo.png" class="logo">
        <div class="musicbtn" :class="{ on:on }"  @click="bgClick"></div>
        <!-- 首页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===1">
            <img class="title animate__animated animate__zoomIn" src="img/title.png">
            <img class="main animate__animated animate__zoomIn" src="img/main.png">
            <div class="start pulsate-bck" @click="start"></div>
            <div class="button_container">
                <img src="img/button2.png" class="button animate__animated animate__fadeInLeft" @click="page=2">
                <img src="img/button3.png" class="button animate__animated animate__fadeInRight" @click="show=2" v-if="startData.prize">
                <img src="img/button3.png" class="button gray animate__animated animate__fadeInRight" @click="show=1" v-if="!startData.prize">
            </div>
        </div>
        <!-- 活动规则页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===2">
            <img src="img/title2.png" class="title2 animate__animated animate__zoomIn">
            <img src="img/rule.png" class="rule">
            <img src="img/button4.png" class="back animate__animated animate__fadeInUp" @click="page=1">
        </div>
        <!-- 上传页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===3">
            <img src="img/step1.png" class="step animate__animated animate__zoomIn">
            <div class="box_area">
                <image-selector ref="imageSelectorRef" v-model="select_img"></image-selector>
                <img src="img/box.png" class="box">
            </div>
            <div class="tip" :style="{opacity:select_img?1:0}">可以手动调整图片大小哦~</div>
            <div class="button_container">
                <img src="img/button5.png" class="button animate__animated animate__fadeInLeft" @click="selectImage">
                <img src="img/button6.png" class="button animate__animated animate__fadeInRight" @touchstart="createdPoster">
            </div>
        </div>
        <!-- 选择flag页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===4">
            <img src="img/bg3.png" class="bg2">
            <img src="img/step2.png" class="step animate__animated animate__zoomIn">
            <div class="nav">
                <img src="img/scan.png?v=1" class="scan" @click="show=6">
                <p class="flag_num" @click="show=6">{<span>{{flagSelect.length}}</span>}</p>
                <div class="tab_area">
                    <div class="tab_area_container">
                        <div class="slide" :style="'left:'+25*(tab-1)+'%'"></div>
                        <div class="tab_item" :class="{active:tab===1}" @click="tab=1">亲情</div>
                        <div class="tab_item" :class="{active:tab===2}" @click="tab=2">生活</div>
                        <div class="tab_item" :class="{active:tab===3}" @click="tab=3">未来</div>
                        <div class="tab_item" :class="{active:tab===4}" @click="tab=4">友情</div>
                    </div>
                </div>
            </div>
            <div class="flag_list">
                <div class="flag_row" v-for="(row, rowIndex) in flagRows" :key="rowIndex">
                    <div class="flag" v-for="(item, index) in row" :key="rowIndex * 2 + index" @click="push(item)">
                        0{{rowIndex * 2 + index + 1}}、{{item}}
                        <span class="flag_separator" v-if="index === 0 && row.length === 2">/</span>
                    </div>
                </div>
            </div>
            <div class="input_area">
                <img src="img/add0.png" class="add0">
                <input type="text" placeholder="也可以在这里写下你的FLAG哦~" v-model="customFlag">
                <img src="img/add.png" class="add" @click="add">
            </div>
            <img src="img/button13.png" class="button13" @click="next(1)">
        </div>
        <!-- 选择选择加油文案页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===5">
            <img src="img/bg3.png" class="bg2">
            <img src="img/step3.png" class="step animate__animated animate__zoomIn">
            <div class="cheering_message_area">
                <div class="cheering_message" v-for="(item, index) in 4" :key="index" :class="{active:cheeringMessageIndex===item}" @click="cheeringMessageIndex=item">
                    <div class="index">{{item}}.</div>
                    <img :src="'img/cheering_message'+item+'.png'" class="cheering_message_img">
                </div>
            </div>
            <img src="img/button13.png" class="button13" @click="next(2)">
        </div>
        <!-- 合成加油文案页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===6">
            <img src="img/bg2.png" class="bg2">
            <div class="poster_area">
                <img :src="poster" class="poster">
                <img :src="'img/cheering_message'+cheeringMessageIndex+'.png'"
                     class="cheering_message draggable"
                     @mousedown="startDrag"
                     @touchstart="startDrag"
                     :style="{transform: `translate(${cheeringMessagePosition.x}px, ${cheeringMessagePosition.y}px)`}">
            </div>
            <div class="tip">可以拖动加油文案哦~</div>
            <div class="button_area">
                <img src="img/button14.png" class="button" @click="next(3)">
            </div>
        </div>
        <!-- 海报生成页 -->
        <div class="page fc" :class="{blur:show}" v-if="page===7">
            <img src="img/bg2.png" class="bg2">
            <div class="poster_area">
                <img :src="poster" class="poster">
                <img :src="'img/cheering_message'+cheeringMessageIndex+'.png'" class="cheering_message"
                :style="{transform: `translate(${cheeringMessagePosition.x}px, ${cheeringMessagePosition.y}px)`}">
            </div>
            <div class="flag_area">
                <img src="img/flag.png" class="flag_img">
                <div class="right">
                    <div class="flag_text" v-for="(item, index) in flagSelect" :key="index">0{{index+1}}、{{item}}</div>
                </div>
            </div>
            <div class="button_area">
                <img src="img/button7.png?v=1" class="button" @click="createdPoster2">
                <!-- <img src="img/button8.png" class="button" @click="getPrize"> -->
            </div>
        </div>
        <!-- 无奖品弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===1">
                <div class="popup popup1">
                    <div class="close2" @click="show=0"></div>
                </div>
            </div>
        </transition>
        <!-- 奖品弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===2">
                <div class="popup popup2">
                    <p class="p1">您的奖品为{{startData.prize}}</p>
                    <div class="qr_area">
                        <img :src="qrImg" class="qr">
                        <!-- <img :src="startData.qr" class="qr"> -->
                    </div>
                    <p class="p2">核销时间：2025.06.21-06.22<br>核销地点：xxxxxx</p>
                    <div class="p3">
                        <span>温馨提示</span>
                        <div class="p3_content">领取奖品时请向工作人员出示此核销码；<br>每个核销码仅可核销一次，请及时兑换礼品。</div>
                    </div>
                    <div class="close2" @click="show=0"></div>
                </div>
            </div>
        </transition>
        <!-- 抽奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===3">
                <div class="popup popup3">
                    <img src="img/button9.png" class="button9" @click="getPrize">
                </div>
            </div>
        </transition>
        <!-- 中奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===4">
                <div class="popup popup4">
                    <div class="p4">抽中<span>{{prizeData.prize}}</span></div>
                    <img src="img/button10.png" class="button10" @click="reload(2)">
                </div>
            </div>
        </transition>
        <!-- 未中奖弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===5">
                <div class="popup popup5">
                    <img src="img/button11.png" class="button11" @click="reload()">
                </div>
            </div>
        </transition>
        <!-- flag弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===6">
                <div class="popup popup6">
                    <div class="select_list_area">
                        <img src="img/popup6_tit.png" class="popup6_tit">
                        <div class="list">
                            <div class="item" v-for="(item, index) in flagSelect" :key="index">
                                <div class="left">0{{index+1}}、{{item}}</div>
                                <div class="close3" @click="flagSelect.splice(index, 1)"></div>
                            </div>
                        </div>
                    </div>
                    <div class="close2" @click="show=0"></div>
                </div>
            </div>
        </transition>
        <!-- flag过多弹窗 -->
        <transition name="van-fade">
            <div class="mask fc" v-if="show===8">
                <div class="popup popup8" @click="show=0">
                    FLAG立的太多啦！<br>少点多实现岂不是更好？
                    <div class="close2" @click="show=0"></div>
                </div>
            </div>
        </transition>
        <!-- 海报弹窗 -->
        <div class="mask fc" v-if="show===7">
            <img :src="poster2" class="poster">
            <div class="white"></div>
            <div class="poster_tip">*长按保存海报*</div>
            <div class="close" @click="show=3"></div>
        </div>
    </div>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/howler.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/preloadjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/qrious.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/dayjs.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/html2canvas.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js?v=20"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script src="js/image-selector-component.js"></script>
    <script>
        const { createApp, ref, watch, nextTick, computed } = Vue
    </script>
    <script>
        window.startData = {
            jihui: '{$jihui}',
            prize: '{$prize}',
            endtime: '{$endtime}',
            qr:'{$qr}',
            id:'{$id}',
            hexiaostatus:'{$hexiaostatus}',//是否核销1是0否
        }

        const app = createApp({
            components: {
                'image-selector': ImageSelectorComponent
            },
            setup() {
                const { on, bgClick } = useBgMusic('https://ztimg.hefei.cc/zt2025/xawdbyjflag/124.mp3')//调用景音乐
                setMockPage && setMockPage()//添加案例提示语
                const page = ref(1) //控制页面
                const show = ref(0) //控制弹窗
                const { userInfo, endtime, writeFlag } = startData
                const opportunity = ref((+startData.jihui)) //控制机会
                const start = () => {
                    if (endtime === '1') return vantAlert('活动未开始')
                    if (endtime === '2') return vantAlert('活动已结束')
                    if (opportunity.value >= 1) {
                        page.value = 3
                        opportunity.value--
                        defaultHttp('gamestart',{},{ status: 1 })
                    } else {
                        vantAlert('您今日的游戏机会已用完，明天再来吧')
                    }
                }
                // 刷新页面功能
                const reload = (num)=>{
                    if(typeof(num)==="number"){
                        sessionStorage.setItem('saveNum',num)
                    }
                    window.location.reload()
                }
                if(sessionStorage.getItem('saveNum')){
                    show.value = +sessionStorage.getItem('saveNum')
                    sessionStorage.removeItem('saveNum')
                }

                const select_img = ref('')
                const imageSelectorRef = ref(null)

                // 调用组件的selectImage方法
                const selectImage = () => {
                    if (imageSelectorRef.value) {
                        imageSelectorRef.value.selectImage()
                    }
                }

                // 抽奖逻辑
                const prizeData = ref({ prize: startData.prize, ad: startData.ad, prizeType: 0 })
                const getPrize = throttle(async () => {
                    const res = await defaultHttp('getprize', {}, { status: 1, msg: '抽中奖品', data: { prize: '定制礼包一份', ad: '青春礼', prizeType: 1 } })
                    if (res.status === 1) {
                        show.value = 4
                        prizeData.value = res.data
                        // defaultHttp('getqr')
                    } else if (res.status === 2) {
                        show.value = 5 //未中奖
                    } else {
                        vantAlert(res.msg, reload)
                    }
                })
                const poster = ref('')
                const poster2 = ref('')
                const createdPoster = ()=>{
                    if(!select_img.value) return
                    var targetDom = document.querySelector('.box_area');
                    html2canvas(targetDom,{
                        useCORS: true, // 使用跨域
                        height: targetDom.offsetHeight, //canvas高
                        width: targetDom.offsetWidth, //canvas宽
                        dpi: window.devicePixelRatio * 2 //设备像素比
                    }).then(canvas=>{
                        poster.value = canvas.toDataURL("image/png")
                        page.value = 4
                    });
                }
                const createdPoster2 = ()=>{
                    var targetDom = document.querySelector('.warp');
                    html2canvas(targetDom,{
                        useCORS: true, // 使用跨域
                        height: targetDom.offsetHeight, //canvas高
                        width: targetDom.offsetWidth, //canvas宽
                        dpi: window.devicePixelRatio * 2, //设备像素比
                        ignoreElements: function(element) {
                            return (element.classList && element.classList.contains('button'))||
                            (element.classList && element.classList.contains('musicbtn'))
                        }
                    }).then(canvas=>{
                        poster2.value = canvas.toDataURL("image/png")
                        show.value = 7
                    });
                }
                const tab = ref(1)
                const flagList = ref([])
                const flagSelect = ref([])
                watch(tab,val=>{
                    if(val===1){
                        flagList.value = ['常回家看看','每周通电话','带父母旅行','学做家乡菜','教爸妈用新APP','陪爸妈体检','生日必陪伴','耐心听唠叨']
                    }
                    if(val===2){
                        flagList.value = ['早睡早起不懒床','积极健身','列个喜爱的书单并完成','拿下驾照','一日三餐好好吃饭','痛快断舍离','培养一个新爱好','学会化妆/穿搭']
                    }
                    if(val===3){
                        flagList.value = ['考研成功','解锁一项职业新技能','考公上岸','每天坚持学习','拿下Dream Offer','经济小独立','副业搞钱启动','早日脱单']
                    }
                    if(val===4){
                        flagList.value = ['好友局多聚聚','和朋友约一场毕业旅行','组队线上剧本杀','一起看场 live house','相互介绍新朋友','见证彼此重要时刻','保持联络不掉线','谁脱单谁请客']
                    }
                },{
                    immediate:true
                })

                // 计算属性：将flagList分组为每行2个flag
                const flagRows = computed(() => {
                    const rows = []
                    for (let i = 0; i < flagList.value.length; i += 2) {
                        const row = []
                        if (flagList.value[i]) {
                            row.push(flagList.value[i])
                        }
                        if (flagList.value[i + 1]) {
                            row.push(flagList.value[i + 1])
                        }
                        rows.push(row)
                    }
                    return rows
                })
                const push = (item)=>{
                    if(flagSelect.value.includes(item)) return
                    if(flagSelect.value.length>=6) return show.value = 8
                    flagSelect.value.push(item)
                }
                const customFlag = ref('')
                const add = ()=>{
                    if(!customFlag.value) return
                    if(flagSelect.value.length>=6) return show.value = 8
                    flagSelect.value.push(customFlag.value)
                    customFlag.value = ''
                }
                const next = (p)=>{
                    if(p===1){
                        if(flagSelect.value.length<1) return vantAlert('请选择至少1个FLAG')
                        page.value = 5
                    }
                    if(p===2){
                        if(!cheeringMessageIndex.value) return vantAlert('请选择加油文案')
                        page.value = 6
                    }
                    if(p===3){
                        page.value = 7
                    }
                }
                const cheeringMessageIndex = ref(0)
                const cheeringMessagePosition = ref({ x: 0, y: 0 })
                const isDragging = ref(false)
                const dragStart = ref({ x: 0, y: 0 })

                const startDrag = (e) => {
                    isDragging.value = true
                    const clientX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX
                    const clientY = e.type === 'touchstart' ? e.touches[0].clientY : e.clientY

                    dragStart.value = {
                        x: clientX - cheeringMessagePosition.value.x,
                        y: clientY - cheeringMessagePosition.value.y
                    }

                    const handleMove = (e) => {
                        if (!isDragging.value) return
                        e.preventDefault()

                        const clientX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX
                        const clientY = e.type === 'touchmove' ? e.touches[0].clientY : e.clientY

                        cheeringMessagePosition.value = {
                            x: clientX - dragStart.value.x,
                            y: clientY - dragStart.value.y
                        }
                    }

                    const handleEnd = () => {
                        isDragging.value = false
                        document.removeEventListener('mousemove', handleMove)
                        document.removeEventListener('mouseup', handleEnd)
                        document.removeEventListener('touchmove', handleMove)
                        document.removeEventListener('touchend', handleEnd)
                    }

                    document.addEventListener('mousemove', handleMove)
                    document.addEventListener('mouseup', handleEnd)
                    document.addEventListener('touchmove', handleMove, { passive: false })
                    document.addEventListener('touchend', handleEnd)
                }
                const qrImg = ref('')
                let url = `https://z.hfurl.cc/zt2025/xawdbyjflag/hexiao?id=${startData.id}`;
                console.log(url);
                const qr = new QRious({
                    element: document.getElementById('qrcode'),
                    value: url,
                    size: 200
                });
                qrImg.value = qr.toDataURL()
                return {
                    startData, page, show,
                    on, bgClick,
                    start, reload,
                    prizeData, getPrize,
                    createdPoster,
                    createdPoster2,
                    poster2,poster, select_img,
                    imageSelectorRef,
                    selectImage,
                    tab,
                    flagList,
                    flagRows,
                    flagSelect,
                    push,
                    customFlag,
                    add,
                    next,
                    cheeringMessageIndex,
                    cheeringMessagePosition,
                    startDrag,
                    qrImg
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
            <!--分享-->
{include file="share"/}
</body>

</html>



