<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>{$zt_name}</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer src="css/polyfill.js?v={php} echo mt_rand(1000,999999);{/php}"></script>
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak>
        <img src="img/logo.png" class="logo">
        <!-- 首页 -->
        <div class="page fc">
            <img class="title animate__animated animate__zoomIn" src="img/title.png">
            <img class="main animate__animated animate__zoomIn" src="img/main.png">
        </div>
    </div>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        const { createApp, ref, watch, nextTick, computed } = Vue
    </script>
    <script>
        const app = createApp({
            setup() {
                vant.showDialog({
                    title:'温馨提示',
                    message: '{$message}',
                    confirmButtonColor:'#4994DF',
                    showCancelButton:true,
                    confirmButtonText:'确认核销',
                    cancelButtonText:'暂不核销',
                }).then(async()=>{
                    try {
                        const result = await defaultHttp('hexiaoapi',{
                            id:'{$id}'
                        })
                        if(result.status === 1) {
                            vantAlert(result.msg || '核销成功',()=>{
                                wx.closeWindow();
                            });
                        } else {
                            vantAlert(result.msg || '提交失败');
                        }
                    } catch(err) {
                        console.error(err);
                        vant.showToast('网络错误,请稍后重试');
                    }
                })
               
                return {
                }
            },
        });
        app.use(vant);
        app.mount('#app');
    </script>
            <!--分享-->
{include file="share"/}
</body>

</html>



