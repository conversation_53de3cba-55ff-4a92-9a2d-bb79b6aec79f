@font-face {
  font-family: '思源宋体';
  src: url(https://ztimg.hefei.cc/static/common/fonts/思源宋体.otf);
}
@font-face {
  font-family: 'HANCHANSHUTI';
  src: url(../HANCHANSHUTI·LONGCANG.OTF);
}
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 2vh;
}
.musicbtn {
  width: 6vh;
  height: 6vh;
  top: 2vh;
  right: 2vh;
  background-image: url(../img/music.png);
  z-index: 11;
}
.logo {
  width: 8vh;
  position: absolute;
  top: 2vh;
  left: 2vh;
  z-index: 11;
}
.warp {
  margin: 0 auto;
  min-height: 102vh;
  height: 100vh;
  width: 60vh;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: '思源宋体';
}
.warp .page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
  color: #fff;
  background: url(../img/bj.jpg?v=1) no-repeat center center / 100% 100%;
}
.warp .page .title {
  margin-top: -6vh;
  width: 37vh;
  z-index: 2;
}
.warp .page .title2 {
  margin-top: 6vh;
  width: 43vh;
}
.warp .page .title3 {
  width: 38vh;
  position: absolute;
  top: 13vh;
}
.warp .page .main {
  margin-top: 2vh;
  width: 52vh;
}
.warp .page .start {
  margin-top: 1vh;
  width: 36vh;
  height: 9vh;
  flex-shrink: 0;
  background: url(../img/start.png) no-repeat center center / 100% 100%;
  z-index: 2;
}
.warp .page .step {
  width: 100%;
}
.warp .page .box_area {
  margin-top: 4vh;
  width: 47vh;
  height: 58vh;
  position: relative;
  overflow: hidden;
}
.warp .page .box_area .box {
  width: 100%;
  position: relative;
  pointer-events: none;
}
.warp .page .poster_area {
  position: relative;
}
.warp .page .poster_area .poster {
  margin-top: 4vh;
  width: 47vh;
}
.warp .page .poster_area .cheering_message {
  width: 23vh;
  position: absolute;
  bottom: 10vh;
  right: 1vh;
}
.warp .page .poster_area .cheering_message.draggable {
  cursor: move;
  cursor: grab;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  touch-action: none;
  pointer-events: auto;
  z-index: 10;
  transition: none;
}
.warp .page .poster_area .cheering_message.draggable:active {
  cursor: grabbing;
}
.warp .page .tip {
  margin-top: 1vh;
  color: #fff;
  font-weight: bold;
  font-size: 2vh;
  letter-spacing: 0vh;
  transition: 0.6s;
}
.warp .page .bg2 {
  width: 60vh;
  position: absolute;
  left: 0;
  bottom: 0;
  pointer-events: none;
}
.warp .page .button_container {
  position: absolute;
  z-index: 2;
  bottom: 4vh;
  display: flex;
  justify-content: center;
}
.warp .page .button_container .button {
  height: 6vh;
  margin: 0 4vh;
}
.warp .page .button_container .gray {
  filter: grayscale(100%);
}
.warp .page .rule {
  margin-top: 4vh;
  width: 60vh;
}
.warp .page .back {
  margin-top: 6vh;
  height: 7vh;
}
.warp .page .nav {
  margin-top: 4vh;
  width: 51vh;
  height: 4vh;
  background: #00A3F0;
  border-radius: 1vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.warp .page .nav .scan {
  width: 15vh;
  margin-left: 1vh;
}
.warp .page .nav .flag_num {
  margin-top: -1vh;
  font-size: 2vh;
  display: flex;
  align-items: center;
}
.warp .page .nav .flag_num span {
  color: #FF9C00;
  font-size: 3vh;
  font-weight: bold;
  margin: 0 1vh;
}
.warp .page .nav .tab_area {
  width: 28vh;
  height: 4vh;
  background: #FFFFF9;
  border-radius: 1vh;
  padding: 0 2vh;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.warp .page .nav .tab_area .tab_area_container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}
.warp .page .nav .tab_area .tab_area_container .slide {
  position: absolute;
  height: 4vh;
  width: 25%;
  background: #FB9B00;
  border-radius: 0vh;
  left: 0;
  transition: 0.3s;
}
.warp .page .nav .tab_area .tab_area_container .tab_item {
  flex: 1;
  color: #C9C9C9;
  height: 4vh;
  text-align: center;
  font-weight: bold;
  position: relative;
  z-index: 1;
  transition: 0.3s;
}
.warp .page .nav .tab_area .tab_area_container .active {
  color: #fff;
}
.warp .page .flag_area {
  margin-top: 2vh;
  width: 46vh;
  height: 20vh;
  background: rgba(255, 255, 255, 0.42);
  border-radius: 1vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
.warp .page .flag_area .flag_img {
  width: 3vh;
}
.warp .page .flag_area .right {
  margin-left: 1vh;
  width: 40vh;
  height: 17vh;
  background: rgba(66, 133, 199, 0.75);
  border-radius: 1vh;
  display: flex;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
}
.warp .page .flag_area .right .flag_text {
  height: 33.3%;
  width: 50%;
  padding-left: 1vh;
  font-size: 2vh;
  display: flex;
  align-items: center;
}
.warp .page .flag_list {
  margin-top: 5vh;
  font-family: 'HANCHANSHUTI';
  width: 100%;
}
.warp .page .flag_list .flag_row {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2vh 0;
  position: relative;
}
.warp .page .flag_list .flag_row .flag {
  color: #fff;
  font-size: 3vh;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}
.warp .page .flag_list .flag_row .flag_separator {
  color: #006CBD;
  font-size: 3vh;
  pointer-events: none;
  margin-right: 1vh;
}
.warp .page .input_area {
  margin-top: 8vh;
  width: 51vh;
  height: 4vh;
  background: #006CBD;
  border-radius: 1vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1vh;
}
.warp .page .input_area .add0 {
  width: 7vh;
}
.warp .page .input_area input {
  font-family: 'HANCHANSHUTI';
  width: 34vh;
  height: 4vh;
  background: #FFFFF9;
  border-radius: 1vh;
  text-align: center;
}
.warp .page .input_area input::-webkit-input-placeholder {
  color: #ADADAD;
}
.warp .page .input_area input:-moz-placeholder {
  color: #ADADAD;
}
.warp .page .input_area input::-moz-placeholder {
  color: #ADADAD;
}
.warp .page .input_area input:-ms-input-placeholder {
  color: #ADADAD;
}
.warp .page .input_area .add {
  width: 5vh;
}
.warp .page .button13 {
  margin-top: 9vh;
  width: 28vh;
}
.warp .page .cheering_message_area {
  margin-top: 6vh;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}
.warp .page .cheering_message_area .cheering_message {
  width: 26vh;
  height: 18vh;
  background: rgba(255, 255, 255, 0.32);
  border-radius: 1vh;
  margin: 1vh;
  padding: 1vh;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
.warp .page .cheering_message_area .cheering_message .index {
  position: absolute;
  top: 1vh;
  left: 2vh;
  font-size: 4vh;
  font-family: 'HANCHANSHUTI';
  color: #FFFFFF;
}
.warp .page .cheering_message_area .cheering_message .cheering_message_img {
  width: 20vh;
}
.warp .page .cheering_message_area .active .cheering_message_img {
  animation: pulsate-bck2 1s ease-in-out infinite both;
}
.warp .page .button_area {
  margin-top: 2vh;
  display: flex;
  align-items: center;
}
.warp .page .button_area .button {
  height: 10vh;
  margin: 0 1vh;
}
.blur {
  filter: blur(1vh);
}
.fc {
  justify-content: center;
}
.mask {
  z-index: 10;
  position: fixed;
  top: 0;
  left: 0;
  min-height: 100vh;
  width: 60vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(18, 45, 29, 0.3);
  transform: translateX(-50%);
  left: 50%;
  color: #fff;
}
.mask .popup {
  margin-top: -1vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .back {
  width: 30vh;
  position: absolute;
  bottom: -4vh;
}
.mask .popup1 {
  width: 50vh;
  height: 29vh;
  background: url(../img/popup1.png) no-repeat center top / 100% 100%;
}
.mask .popup2 {
  margin-top: -12vh;
  width: 42vh;
  height: 71vh;
  background: url(../img/popup2.png) no-repeat center top / 100% 100%;
}
.mask .popup2 .p1 {
  position: absolute;
  top: 22vh;
  font-size: 3vh;
}
.mask .popup2 .qr_area {
  position: absolute;
  top: 31vh;
}
.mask .popup2 .qr_area .qr {
  width: 16vh;
}
.mask .popup2 .p2 {
  color: #5E4538;
  position: absolute;
  top: 50vh;
  text-align: center;
}
.mask .popup2 .p3 {
  color: #5E4538;
  position: absolute;
  top: 59vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.mask .popup2 .p3 span {
  display: block;
  padding: 0 1vh;
  border-radius: 4vh;
  background-color: #fff;
  color: #005F9C;
}
.mask .popup2 .p3 .p3_content {
  margin-top: 1vh;
  text-align: center;
  font-size: 2vh;
}
.mask .popup3 {
  width: 60vh;
  height: 20vh;
  background: url(../img/popup3.png) no-repeat center top / 100% 100%;
}
.mask .popup3 .button9 {
  width: 31vh;
  position: absolute;
  bottom: -10vh;
}
.mask .popup4 {
  margin-top: -12vh;
  width: 42vh;
  height: 49vh;
  background: url(../img/popup4.png) no-repeat center top / 100% 100%;
}
.mask .popup4 .p4 {
  margin-top: 18vh;
  color: #5e4538;
  font-weight: bold;
  font-size: 2vh;
}
.mask .popup4 .p4 span {
  color: #dc2a18;
  font-size: 3vh;
}
.mask .popup4 .button10 {
  width: 22vh;
  position: absolute;
  bottom: 2vh;
}
.mask .popup5 {
  width: 39vh;
  height: 37vh;
  background: url(../img/popup5.png) no-repeat center top / 100% 100%;
}
.mask .popup5 .button11 {
  width: 22vh;
  position: absolute;
  bottom: 2vh;
}
.mask .popup6 {
  width: 49vh;
  height: 54vh;
  background: rgba(255, 255, 255, 0.42);
  border-radius: 1vh;
  border: 1px solid #000000;
}
.mask .popup6 .select_list_area {
  width: 46vh;
  height: 51vh;
  background: rgba(66, 133, 199, 0.9);
  border-radius: 1vh;
  border: 1px solid #000000;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.mask .popup6 .select_list_area .popup6_tit {
  margin-top: 2vh;
  width: 20vh;
}
.mask .popup6 .select_list_area .list {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.mask .popup6 .select_list_area .list .item {
  margin-top: 4vh;
  width: 100%;
  padding: 0 4vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.mask .popup6 .select_list_area .list .item .close3 {
  width: 3vh;
  height: 3vh;
  background: url(../img/close.png) no-repeat center center / 100% 100%;
}
.mask .popup8 {
  width: 42vh;
  height: 11vh;
  background: rgba(255, 255, 255, 0.79);
  border-radius: 1vh;
  border: 1px solid #000100;
  font-size: 3vh;
  color: #906100;
  line-height: 5vh;
  text-align: center;
  font-weight: bold;
}
.mask .poster {
  width: 60vh;
  animation: sc 0.5s ease-in-out forwards;
  animation-delay: 0.5s;
}
.mask .white {
  width: 60vh;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  background-color: white;
  /* 设置为你想要的背景颜色 */
  animation: flash 0.5s ease-in-out forwards;
  /* 定义动画名称、时长和缓动函数 */
  pointer-events: none;
}
.mask .poster_tip {
  color: #fff;
  position: absolute;
  bottom: 16vh;
  font-weight: bold;
  letter-spacing: 0vh;
  animation: flash2 1s ease-in-out forwards;
}
.mask .close {
  width: 5vh;
  height: 5vh;
  background: url(../img/close.png) no-repeat center center / 100% 100%;
  position: absolute;
  bottom: 6vh;
  animation: flash2 1s ease-in-out forwards;
}
.mask .close2 {
  width: 6vh;
  height: 6vh;
  background: url(../img/close.png) no-repeat center center / 100% 100%;
  position: absolute;
  bottom: -9vh;
}
.blink-2 {
  animation: blink-2 1s linear infinite both;
}
@keyframes blink-2 {
  0%,
  10%,
  90%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.2;
  }
}
@keyframes flash {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes flash2 {
  0%,
  99% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes sc {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.7) translateY(-6vh);
  }
}
@keyframes pulsate-bck2 {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
